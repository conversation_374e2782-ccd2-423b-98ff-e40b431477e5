const sql = require('mssql');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const config = {
    server: process.env.DB_SERVER,
    database: 'master', // Connect to master first
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: parseInt(process.env.DB_PORT) || 1433,
    options: {
        encrypt: false,
        trustServerCertificate: true
    }
};

async function setupDatabase() {
    let pool;
    
    try {
        console.log('🔌 Connecting to SQL Server...');
        pool = await sql.connect(config);
        console.log('✅ Connected to SQL Server');

        // Create database if not exists
        console.log('🗄️ Creating database...');
        await pool.request().query(`
            IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'adminpanel_cabal35')
            BEGIN
                CREATE DATABASE adminpanel_cabal35;
                PRINT 'Database adminpanel_cabal35 created successfully';
            END
            ELSE
            BEGIN
                PRINT 'Database adminpanel_cabal35 already exists';
            END
        `);

        // Switch to the new database
        await pool.close();
        config.database = 'adminpanel_cabal35';
        pool = await sql.connect(config);
        console.log('✅ Connected to adminpanel_cabal35 database');

        // Create tables
        console.log('📋 Creating tables...');
        
        // Users table
        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
            BEGIN
                CREATE TABLE Users (
                    UserID INT IDENTITY(1,1) PRIMARY KEY,
                    Username NVARCHAR(50) NOT NULL UNIQUE,
                    Email NVARCHAR(100) NOT NULL UNIQUE,
                    PasswordHash NVARCHAR(255) NOT NULL,
                    FirstName NVARCHAR(50),
                    LastName NVARCHAR(50),
                    Role NVARCHAR(20) DEFAULT 'user' CHECK (Role IN ('admin', 'user', 'moderator')),
                    IsActive BIT DEFAULT 1,
                    CreatedAt DATETIME2 DEFAULT GETDATE(),
                    UpdatedAt DATETIME2 DEFAULT GETDATE(),
                    LastLoginAt DATETIME2 NULL
                );
                PRINT 'Users table created';
            END
            ELSE
            BEGIN
                PRINT 'Users table already exists';
            END
        `);

        // UserSessions table
        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserSessions' AND xtype='U')
            BEGIN
                CREATE TABLE UserSessions (
                    SessionID INT IDENTITY(1,1) PRIMARY KEY,
                    UserID INT NOT NULL,
                    TokenHash NVARCHAR(255) NOT NULL,
                    ExpiresAt DATETIME2 NOT NULL,
                    CreatedAt DATETIME2 DEFAULT GETDATE(),
                    IsActive BIT DEFAULT 1,
                    FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE CASCADE
                );
                PRINT 'UserSessions table created';
            END
            ELSE
            BEGIN
                PRINT 'UserSessions table already exists';
            END
        `);

        // LoginHistory table
        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LoginHistory' AND xtype='U')
            BEGIN
                CREATE TABLE LoginHistory (
                    LogID INT IDENTITY(1,1) PRIMARY KEY,
                    UserID INT,
                    LoginTime DATETIME2 DEFAULT GETDATE(),
                    IPAddress NVARCHAR(45),
                    UserAgent NVARCHAR(500),
                    LoginStatus NVARCHAR(20) DEFAULT 'success' CHECK (LoginStatus IN ('success', 'failed')),
                    FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE SET NULL
                );
                PRINT 'LoginHistory table created';
            END
            ELSE
            BEGIN
                PRINT 'LoginHistory table already exists';
            END
        `);

        // Create indexes
        console.log('📊 Creating indexes...');
        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Username')
                CREATE INDEX IX_Users_Username ON Users(Username);
            
            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Email')
                CREATE INDEX IX_Users_Email ON Users(Email);
        `);

        // Create demo users
        console.log('👥 Creating demo users...');
        
        // Clear existing demo users
        await pool.request().query(`
            DELETE FROM Users WHERE Username IN ('admin', 'user1', 'moderator1')
        `);

        const demoUsers = [
            { username: 'admin', email: '<EMAIL>', password: 'admin123', firstName: 'Admin', lastName: 'User', role: 'admin' },
            { username: 'user1', email: '<EMAIL>', password: 'user123', firstName: 'John', lastName: 'Doe', role: 'user' },
            { username: 'moderator1', email: '<EMAIL>', password: 'mod123', firstName: 'Jane', lastName: 'Smith', role: 'moderator' }
        ];

        for (const user of demoUsers) {
            const passwordHash = await bcrypt.hash(user.password, 10);
            
            await pool.request()
                .input('username', sql.NVarChar, user.username)
                .input('email', sql.NVarChar, user.email)
                .input('passwordHash', sql.NVarChar, passwordHash)
                .input('firstName', sql.NVarChar, user.firstName)
                .input('lastName', sql.NVarChar, user.lastName)
                .input('role', sql.NVarChar, user.role)
                .query(`
                    INSERT INTO Users (Username, Email, PasswordHash, FirstName, LastName, Role, IsActive)
                    VALUES (@username, @email, @passwordHash, @firstName, @lastName, @role, 1)
                `);
            
            console.log(`✅ Created user: ${user.username} (${user.role})`);
        }

        console.log('\n🎉 Database setup completed successfully!');
        console.log('\nDemo Credentials:');
        console.log('- admin / admin123 (Admin)');
        console.log('- user1 / user123 (User)');
        console.log('- moderator1 / mod123 (Moderator)');

    } catch (error) {
        console.error('❌ Database setup failed:', error);
    } finally {
        if (pool) {
            await pool.close();
        }
        process.exit(0);
    }
}

setupDatabase();
