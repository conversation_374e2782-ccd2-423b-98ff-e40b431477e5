// Mock User model for testing without database connection
class MockUser {
    constructor(userData) {
        this.userNum = userData.userNum || userData.UserNum;
        this.id = userData.id || userData.ID;
        this.username = this.id;
        this.email = this.id + '@cabal.com';
        this.firstName = this.id;
        this.lastName = '';
        this.role = this.determineRole(this.id);
        this.isActive = true;
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.lastLoginAt = null;
    }

    determineRole(id) {
        if (id && id.toLowerCase().includes('admin')) {
            return 'admin';
        } else if (id && id.toLowerCase().includes('mod')) {
            return 'moderator';
        }
        return 'user';
    }

    // Mock users data
    static getMockUsers() {
        return [
            { userNum: 1, ID: 'admin', password: 'admin123' },
            { userNum: 2, ID: 'user1', password: 'user123' },
            { userNum: 3, ID: 'moderator1', password: 'mod123' },
            { userNum: 4, ID: 'testuser', password: 'test123' }
        ];
    }

    static async findByUsernameOrEmail(identifier) {
        try {
            const mockUsers = this.getMockUsers();
            const userData = mockUsers.find(user => user.ID === identifier);
            
            if (userData) {
                return new MockUser(userData);
            }
            
            return null;
        } catch (error) {
            console.error('Error finding user:', error);
            throw error;
        }
    }

    static async findById(userNum) {
        try {
            const mockUsers = this.getMockUsers();
            const userData = mockUsers.find(user => user.userNum === userNum);
            
            if (userData) {
                return new MockUser(userData);
            }
            
            return null;
        } catch (error) {
            console.error('Error finding user by ID:', error);
            throw error;
        }
    }

    static async create(userData) {
        try {
            const { username, password } = userData;
            const mockUsers = this.getMockUsers();
            const newUserNum = Math.max(...mockUsers.map(u => u.userNum)) + 1;
            
            const newUserData = {
                userNum: newUserNum,
                ID: username,
                password: password
            };
            
            return new MockUser(newUserData);
        } catch (error) {
            console.error('Error creating user:', error);
            throw error;
        }
    }

    async validatePassword(password) {
        try {
            const mockUsers = MockUser.getMockUsers();
            const userData = mockUsers.find(user => user.ID === this.id);
            
            return userData && userData.password === password;
        } catch (error) {
            console.error('Error validating password:', error);
            throw error;
        }
    }

    async updateLastLogin() {
        try {
            this.lastLoginAt = new Date();
            console.log(`User ${this.id} logged in at ${this.lastLoginAt}`);
        } catch (error) {
            console.error('Error updating last login:', error);
            throw error;
        }
    }

    static async getAll(page = 1, limit = 10) {
        try {
            const mockUsers = this.getMockUsers();
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedUsers = mockUsers.slice(startIndex, endIndex);
            
            return {
                users: paginatedUsers.map(user => new MockUser(user)),
                total: mockUsers.length,
                page,
                limit,
                totalPages: Math.ceil(mockUsers.length / limit)
            };
        } catch (error) {
            console.error('Error getting all users:', error);
            throw error;
        }
    }

    toJSON() {
        return {
            userID: this.userNum,
            userNum: this.userNum,
            id: this.id,
            username: this.username,
            email: this.email,
            firstName: this.firstName,
            lastName: this.lastName,
            role: this.role,
            isActive: this.isActive,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            lastLoginAt: this.lastLoginAt
        };
    }
}

module.exports = MockUser;
