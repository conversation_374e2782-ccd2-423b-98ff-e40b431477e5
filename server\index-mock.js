const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Use mock routes instead of database routes
const authRoutes = require('./routes/auth-mock');
const userRoutes = require('./routes/users-mock');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        success: true, 
        message: 'Mock Server is running',
        timestamp: new Date().toISOString(),
        mode: 'mock'
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ 
        success: false, 
        message: 'Something went wrong!' 
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({ 
        success: false, 
        message: 'Route not found' 
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Mock Server running on port ${PORT}`);
    console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
    console.log('🎭 Running in MOCK mode - no database required');
    console.log('\nMock Users:');
    console.log('- admin / admin123 (Admin)');
    console.log('- user1 / user123 (User)');
    console.log('- moderator1 / mod123 (Moderator)');
    console.log('- testuser / test123 (User)');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down mock server...');
    process.exit(0);
});
