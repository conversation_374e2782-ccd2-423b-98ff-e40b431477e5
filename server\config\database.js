const sql = require('mssql');
require('dotenv').config();

const config = {
    server: process.env.DB_SERVER || 'localhost',
    database: process.env.DB_DATABASE || 'Account',
    user: process.env.DB_USER || 'sa',
    password: process.env.DB_PASSWORD || 'mataassWdcw51EWxc',
    port: parseInt(process.env.DB_PORT) || 1433,
    options: {
        encrypt: false, // Use true for Azure
        trustServerCertificate: true, // Use true for local dev / self-signed certs
        enableArithAbort: true,
        instanceName: '' // Leave empty for default instance
    },
    pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
    },
    connectionTimeout: 60000,
    requestTimeout: 60000
};

class Database {
    constructor() {
        this.pool = null;
    }

    async connect() {
        try {
            this.pool = await sql.connect(config);
            console.log('✅ Connected to SQL Server successfully');
            return this.pool;
        } catch (error) {
            console.error('❌ Database connection failed:', error);
            throw error;
        }
    }

    async disconnect() {
        try {
            if (this.pool) {
                await this.pool.close();
                console.log('🔌 Disconnected from SQL Server');
            }
        } catch (error) {
            console.error('❌ Error disconnecting from database:', error);
        }
    }

    getPool() {
        return this.pool;
    }

    async query(queryString, params = {}) {
        try {
            const request = this.pool.request();
            
            // Add parameters to request
            Object.keys(params).forEach(key => {
                request.input(key, params[key]);
            });

            const result = await request.query(queryString);
            return result;
        } catch (error) {
            console.error('❌ Query execution failed:', error);
            throw error;
        }
    }
}

module.exports = new Database();
