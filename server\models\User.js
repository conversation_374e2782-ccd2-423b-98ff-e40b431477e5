const database = require('../config/database');

class User {
    constructor(userData) {
        this.userNum = userData.UserNum;
        this.id = userData.ID;
        this.username = userData.ID; // Use ID as username for compatibility
        this.email = userData.ID + '@cabal.com'; // Generate email from ID
        this.firstName = userData.ID;
        this.lastName = '';
        this.role = this.determineRole(userData.ID); // Determine role based on ID
        this.isActive = true;
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.lastLoginAt = null;
    }

    // Determine user role based on ID pattern
    determineRole(id) {
        if (id && id.toLowerCase().includes('admin')) {
            return 'admin';
        } else if (id && id.toLowerCase().includes('mod')) {
            return 'moderator';
        }
        return 'user';
    }

    // ค้นหาผู้ใช้ด้วย ID
    static async findByUsernameOrEmail(identifier) {
        try {
            const query = `
                SELECT UserNum, ID FROM cabal_auth_table
                WHERE ID = @identifier
            `;

            const result = await database.query(query, { identifier });

            if (result.recordset.length > 0) {
                return new User(result.recordset[0]);
            }

            return null;
        } catch (error) {
            console.error('Error finding user:', error);
            throw error;
        }
    }

    // ค้นหาผู้ใช้ด้วย UserNum
    static async findById(userNum) {
        try {
            const query = `SELECT UserNum, ID FROM cabal_auth_table WHERE UserNum = @userNum`;
            const result = await database.query(query, { userNum });

            if (result.recordset.length > 0) {
                return new User(result.recordset[0]);
            }

            return null;
        } catch (error) {
            console.error('Error finding user by ID:', error);
            throw error;
        }
    }

    // สร้างผู้ใช้ใหม่ (สำหรับ cabal_auth_table)
    static async create(userData) {
        try {
            const { username, password } = userData;

            const query = `
                INSERT INTO cabal_auth_table (ID, Password)
                OUTPUT INSERTED.UserNum, INSERTED.ID
                VALUES (@username, PWDENCRYPT(@password))
            `;

            const result = await database.query(query, {
                username,
                password
            });

            if (result.recordset.length > 0) {
                return new User(result.recordset[0]);
            }

            return null;
        } catch (error) {
            console.error('Error creating user:', error);
            throw error;
        }
    }

    // ตรวจสอบรหัสผ่าน (ใช้ PWDCOMPARE ของ SQL Server)
    async validatePassword(password) {
        try {
            const query = `
                SELECT COUNT(*) as isValid
                FROM cabal_auth_table
                WHERE ID = @id AND PWDCOMPARE(@password, Password) = 1
            `;

            const result = await database.query(query, {
                id: this.id,
                password: password
            });

            return result.recordset[0].isValid > 0;
        } catch (error) {
            console.error('Error validating password:', error);
            throw error;
        }
    }

    // อัพเดท Last Login (ไม่จำเป็นสำหรับ cabal_auth_table)
    async updateLastLogin() {
        try {
            // For cabal_auth_table, we don't have LastLoginAt field
            // Just update the local property
            this.lastLoginAt = new Date();
            console.log(`User ${this.id} logged in at ${this.lastLoginAt}`);
        } catch (error) {
            console.error('Error updating last login:', error);
            throw error;
        }
    }

    // ดึงข้อมูลผู้ใช้ทั้งหมด (สำหรับ admin)
    static async getAll(page = 1, limit = 10) {
        try {
            const offset = (page - 1) * limit;

            const query = `
                SELECT UserNum, ID
                FROM cabal_auth_table
                ORDER BY UserNum DESC
                OFFSET @offset ROWS
                FETCH NEXT @limit ROWS ONLY
            `;

            const countQuery = `SELECT COUNT(*) as total FROM cabal_auth_table`;

            const [result, countResult] = await Promise.all([
                database.query(query, { offset, limit }),
                database.query(countQuery)
            ]);

            return {
                users: result.recordset.map(user => new User(user)),
                total: countResult.recordset[0].total,
                page,
                limit,
                totalPages: Math.ceil(countResult.recordset[0].total / limit)
            };
        } catch (error) {
            console.error('Error getting all users:', error);
            throw error;
        }
    }

    // แปลงเป็น JSON (ไม่รวม sensitive data)
    toJSON() {
        return {
            userID: this.userNum,
            userNum: this.userNum,
            id: this.id,
            username: this.username,
            email: this.email,
            firstName: this.firstName,
            lastName: this.lastName,
            role: this.role,
            isActive: this.isActive,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            lastLoginAt: this.lastLoginAt
        };
    }
}

module.exports = User;
