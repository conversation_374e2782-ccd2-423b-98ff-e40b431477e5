# Database Setup Guide

## ปัจจุบันระบบทำงานใน MOCK MODE

ระบบได้ถูกตั้งค่าให้ทำงานแบบ Hybrid โดยจะพยายามเชื่อมต่อ database จริงก่อน หากไม่สำเร็จจะใช้ Mock Mode อัตโนมัติ

## การตรวจสอบสถานะ

```bash
# ตรวจสอบสถานะระบบ
curl http://localhost:5000/api/status

# ตรวจสอบ health
curl http://localhost:5000/api/health
```

## การแก้ไขปัญหาการเชื่อมต่อ Database

### 1. ตรวจสอบ SQL Server Service

```cmd
# ตรวจสอบว่า SQL Server ทำงานอยู่หรือไม่
sc query MSSQLSERVER
# หรือ
sc query "SQL Server (SQLEXPRESS)"
```

### 2. ตรวจสอบ TCP/IP และ Port

```cmd
# ตรวจสอบ port ที่ SQL Server ใช้
netstat -an | findstr :1433
```

### 3. ตรวจสอบ SQL Server Configuration Manager

- เปิด SQL Server Configuration Manager
- ตรวจสอบ SQL Server Network Configuration
- เปิดใช้งาน TCP/IP
- ตั้งค่า Port เป็น 1433

### 4. ตรวจสอบ Authentication Mode

```sql
-- ตรวจสอบ authentication mode
SELECT SERVERPROPERTY('IsIntegratedSecurityOnly') as [Is Windows Auth Only];

-- ถ้าเป็น 1 = Windows Authentication Only
-- ถ้าเป็น 0 = Mixed Mode (SQL Server + Windows)
```

### 5. สร้าง/ตรวจสอบ User Account

```sql
-- สร้าง login สำหรับ mata33
CREATE LOGIN mata33 WITH PASSWORD = 'mataassWdcw51EWxc';

-- ให้สิทธิ์เข้าถึง Account database
USE Account;
CREATE USER mata33 FOR LOGIN mata33;

-- ให้สิทธิ์ในการอ่านข้อมูล
ALTER ROLE db_datareader ADD MEMBER mata33;
ALTER ROLE db_datawriter ADD MEMBER mata33;
```

### 6. ทดสอบการเชื่อมต่อ

```bash
# ทดสอบการเชื่อมต่อ
cd server
node scripts/testConnection.js
```

### 7. การตั้งค่า .env

```env
# Database Configuration
DB_SERVER=localhost
DB_DATABASE=Account
DB_USER=mata33
DB_PASSWORD=mataassWdcw51EWxc
DB_PORT=1433

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=24h

# Server Configuration
PORT=5000
NODE_ENV=development
```

## การรัน Server

### Hybrid Mode (แนะนำ)
```bash
cd server
node index-hybrid.js
```
- จะพยายามเชื่อมต่อ database จริงก่อน
- หากไม่สำเร็จจะใช้ Mock Mode

### Mock Mode เท่านั้น
```bash
cd server
node index-mock.js
```

### Database Mode เท่านั้น
```bash
cd server
node index.js
```

## Mock Users (สำหรับทดสอบ)

เมื่อระบบทำงานใน Mock Mode จะมี users ดังนี้:

- **admin** / admin123 (Admin)
- **user1** / user123 (User)  
- **moderator1** / mod123 (Moderator)
- **testuser** / test123 (User)

## การเปลี่ยนจาก Mock เป็น Database

เมื่อ database พร้อมใช้งาน:

1. แก้ไขปัญหาการเชื่อมต่อตามขั้นตอนข้างต้น
2. รีสตาร์ท hybrid server
3. ระบบจะเปลี่ยนเป็น Database Mode อัตโนมัติ

## การตรวจสอบข้อมูลใน cabal_auth_table

```sql
-- ดูข้อมูล users ทั้งหมด
SELECT UserNum, ID FROM cabal_auth_table ORDER BY UserNum;

-- ทดสอบ password validation
SELECT UserNum, ID 
FROM cabal_auth_table 
WHERE ID = 'your_username' 
AND PWDCOMPARE('your_password', Password) = 1;
```

## หมายเหตุ

- ระบบใช้ `PWDCOMPARE` function ของ SQL Server สำหรับตรวจสอบรหัสผ่าน
- ไม่ต้องกังวลเรื่อง password hashing เพราะ SQL Server จัดการให้
- Mock Mode ใช้สำหรับทดสอบและพัฒนาเท่านั้น
