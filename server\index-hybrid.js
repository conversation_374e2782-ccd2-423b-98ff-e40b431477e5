const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Try to connect to database, fallback to mock if failed
let useMock = false;
let database = null;

async function initializeDatabase() {
    try {
        database = require('./config/database');
        await database.connect();
        console.log('✅ Connected to SQL Server - Using REAL database');
        
        // Test query to verify cabal_auth_table exists
        await database.query('SELECT TOP 1 UserNum, ID FROM cabal_auth_table');
        console.log('✅ cabal_auth_table verified');
        
        useMock = false;
    } catch (error) {
        console.warn('⚠️ Database connection failed, falling back to MOCK mode');
        console.warn('Error:', error.message);
        useMock = true;
        
        if (database) {
            await database.disconnect();
        }
    }
}

// Dynamic route loading based on database availability
async function setupRoutes() {
    await initializeDatabase();
    
    if (useMock) {
        console.log('🎭 Loading MOCK routes');
        const authRoutes = require('./routes/auth-mock');
        const userRoutes = require('./routes/users-mock');
        app.use('/api/auth', authRoutes);
        app.use('/api/users', userRoutes);
    } else {
        console.log('🗄️ Loading DATABASE routes');
        const authRoutes = require('./routes/auth');
        const userRoutes = require('./routes/users');
        app.use('/api/auth', authRoutes);
        app.use('/api/users', userRoutes);
    }
}

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        success: true, 
        message: `Hybrid Server is running`,
        timestamp: new Date().toISOString(),
        mode: useMock ? 'mock' : 'database',
        database: useMock ? 'Mock Users' : 'SQL Server (cabal_auth_table)'
    });
});

// Status endpoint
app.get('/api/status', (req, res) => {
    res.json({
        success: true,
        mode: useMock ? 'mock' : 'database',
        database: {
            connected: !useMock,
            type: useMock ? 'Mock Users' : 'SQL Server',
            table: useMock ? 'In-Memory' : 'cabal_auth_table'
        },
        features: {
            login: true,
            register: useMock, // Only available in mock mode
            userManagement: true
        }
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ 
        success: false, 
        message: 'Something went wrong!',
        mode: useMock ? 'mock' : 'database'
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({ 
        success: false, 
        message: 'Route not found' 
    });
});

// Start server
async function startServer() {
    try {
        await setupRoutes();
        
        app.listen(PORT, () => {
            console.log(`🚀 Hybrid Server running on port ${PORT}`);
            console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
            console.log(`📊 Status: http://localhost:${PORT}/api/status`);
            
            if (useMock) {
                console.log('\n🎭 MOCK MODE - Demo Users:');
                console.log('- admin / admin123 (Admin)');
                console.log('- user1 / user123 (User)');
                console.log('- moderator1 / mod123 (Moderator)');
                console.log('- testuser / test123 (User)');
            } else {
                console.log('\n🗄️ DATABASE MODE - Using cabal_auth_table');
                console.log('- Use existing users from your database');
                console.log('- Password validation via PWDCOMPARE');
            }
        });
    } catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down server...');
    if (!useMock && database) {
        await database.disconnect();
    }
    process.exit(0);
});

startServer();
