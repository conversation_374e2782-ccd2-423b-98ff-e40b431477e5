const sql = require('mssql');
require('dotenv').config();

const config = {
    server: process.env.DB_SERVER,
    database: process.env.DB_DATABASE,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: parseInt(process.env.DB_PORT) || 1433,
    options: {
        encrypt: false,
        trustServerCertificate: true
    }
};

async function testConnection() {
    console.log('Testing database connection...');
    console.log('Config:', {
        server: config.server,
        database: config.database,
        user: config.user,
        password: config.password ? '***' : 'empty',
        port: config.port
    });

    try {
        const pool = await sql.connect(config);
        console.log('✅ Connected to SQL Server successfully');
        
        // Test query
        const result = await pool.request().query('SELECT COUNT(*) as count FROM cabal_auth_table');
        console.log(`✅ Found ${result.recordset[0].count} users in cabal_auth_table`);
        
        // Test a specific user
        const testUser = await pool.request()
            .input('id', sql.VarChar, 'admin')
            .query('SELECT UserNum, ID FROM cabal_auth_table WHERE ID = @id');
        
        if (testUser.recordset.length > 0) {
            console.log('✅ Found test user:', testUser.recordset[0]);
        } else {
            console.log('⚠️ No test user found with ID "admin"');
        }
        
        await pool.close();
        console.log('✅ Connection test completed successfully');
        
    } catch (error) {
        console.error('❌ Connection test failed:', error.message);
        console.error('Full error:', error);
    }
    
    process.exit(0);
}

testConnection();
