const sql = require('mssql');
require('dotenv').config();

// Try multiple connection configurations
const configs = [
    // Configuration 1: Original credentials
    {
        server: 'localhost',
        database: 'Account',
        user: 'sa',
        password: 'mataassWdcw51EWxc',
        port: 1433,
        options: {
            encrypt: false,
            trustServerCertificate: true,
            enableArithAbort: true
        }
    },
    // Configuration 2: Different port
    {
        server: 'localhost',
        database: 'Account',
        user: 'sa',
        password: 'mataassWdcw51EWxc',
        port: 1434,
        options: {
            encrypt: false,
            trustServerCertificate: true,
            enableArithAbort: true
        }
    },
    // Configuration 3: Named instance
    {
        server: 'localhost\\MSSQLSERVER',
        database: 'Account',
        user: 'sa',
        password: 'mataassWdcw51EWxc',
        options: {
            encrypt: false,
            trustServerCertificate: true,
            enableArithAbort: true
        }
    },
    // Configuration 4: SQLEXPRESS with port
    {
        server: 'localhost\\SQLEXPRESS',
        database: 'Account',
        user: 'sa',
        password: 'mataassWdcw51EWxc',
        port: 1433,
        options: {
            encrypt: false,
            trustServerCertificate: true,
            enableArithAbort: true
        }
    },
    // Configuration 5: Windows Authentication
    {
        server: 'localhost',
        database: 'Account',
        options: {
            encrypt: false,
            trustServerCertificate: true,
            enableArithAbort: true,
            trustedConnection: true
        }
    },
    // Configuration 6: 127.0.0.1
    {
        server: '127.0.0.1',
        database: 'Account',
        user: 'sa',
        password: 'mataassWdcw51EWxc',
        port: 1433,
        options: {
            encrypt: false,
            trustServerCertificate: true,
            enableArithAbort: true
        }
    }
];

async function testConnection() {
    console.log('Testing database connection with multiple configurations...');

    for (let i = 0; i < configs.length; i++) {
        const config = configs[i];
        console.log(`\n--- Testing Configuration ${i + 1} ---`);
        console.log('Config:', {
            server: config.server,
            database: config.database,
            user: config.user || 'Windows Auth',
            password: config.password ? '***' : 'Windows Auth',
            port: config.port,
            trustedConnection: config.options?.trustedConnection || false
        });

        try {
            const pool = await sql.connect(config);
            console.log('✅ Connected to SQL Server successfully');

            // Test query
            const result = await pool.request().query('SELECT COUNT(*) as count FROM cabal_auth_table');
            console.log(`✅ Found ${result.recordset[0].count} users in cabal_auth_table`);

            // List some users
            const users = await pool.request().query('SELECT TOP 5 UserNum, ID FROM cabal_auth_table ORDER BY UserNum');
            console.log('✅ Sample users:');
            users.recordset.forEach(user => {
                console.log(`  - UserNum: ${user.UserNum}, ID: ${user.ID}`);
            });

            await pool.close();
            console.log('✅ Connection test completed successfully');

            // Save working config to .env
            console.log('\n🎉 Connection successful! Updating .env file...');
            updateEnvFile(config);

            process.exit(0);

        } catch (error) {
            console.error(`❌ Configuration ${i + 1} failed:`, error.message);
            await sql.close();
        }
    }

    console.error('\n❌ All connection configurations failed');
    process.exit(1);
}

function updateEnvFile(workingConfig) {
    const fs = require('fs');
    const path = require('path');

    let envContent = `# Database Configuration (Auto-generated)
DB_SERVER=${workingConfig.server}
DB_DATABASE=${workingConfig.database}
DB_PORT=${workingConfig.port}
`;

    if (workingConfig.user && workingConfig.password) {
        envContent += `DB_USER=${workingConfig.user}
DB_PASSWORD=${workingConfig.password}
`;
    } else {
        envContent += `# Using Windows Authentication
DB_USER=
DB_PASSWORD=
`;
    }

    envContent += `
# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_this_in_production
JWT_EXPIRE=24h

# Server Configuration
PORT=5000
NODE_ENV=development
`;

    const envPath = path.join(__dirname, '../.env');
    fs.writeFileSync(envPath, envContent);
    console.log('✅ .env file updated with working configuration');
}

testConnection();
