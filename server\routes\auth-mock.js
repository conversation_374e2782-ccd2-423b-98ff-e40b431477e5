const express = require('express');
const { body, validationResult } = require('express-validator');
const MockUser = require('../models/MockUser');
const { generateToken, authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Validation middleware
const loginValidation = [
    body('username').notEmpty().withMessage('Username or email is required'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters')
];

const registerValidation = [
    body('username').isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
    body('email').isEmail().withMessage('Please provide a valid email'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required')
];

// POST /api/auth/login
router.post('/login', loginValidation, async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { username, password } = req.body;

        // Find user
        const user = await MockUser.findByUsernameOrEmail(username);
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        // Validate password
        const isValidPassword = await user.validatePassword(password);
        if (!isValidPassword) {
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        // Update last login
        await user.updateLastLogin();

        // Generate JWT token
        const token = generateToken({
            userID: user.userNum,
            userNum: user.userNum,
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role
        });

        res.json({
            success: true,
            message: 'Login successful',
            data: {
                user: user.toJSON(),
                token,
                expiresIn: process.env.JWT_EXPIRE || '24h'
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// POST /api/auth/register
router.post('/register', registerValidation, async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { username, email, password, firstName, lastName, role } = req.body;

        // Check if user already exists
        const existingUser = await MockUser.findByUsernameOrEmail(username);
        if (existingUser) {
            return res.status(409).json({
                success: false,
                message: 'Username already exists'
            });
        }

        // Create new user
        const newUser = await MockUser.create({
            username,
            email,
            password,
            firstName,
            lastName,
            role: role || 'user'
        });

        if (!newUser) {
            return res.status(500).json({
                success: false,
                message: 'Failed to create user'
            });
        }

        // Generate JWT token
        const token = generateToken({
            userID: newUser.userNum,
            userNum: newUser.userNum,
            id: newUser.id,
            username: newUser.username,
            email: newUser.email,
            role: newUser.role
        });

        res.status(201).json({
            success: true,
            message: 'User registered successfully',
            data: {
                user: newUser.toJSON(),
                token,
                expiresIn: process.env.JWT_EXPIRE || '24h'
            }
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// GET /api/auth/me - Get current user info
router.get('/me', authenticateToken, async (req, res) => {
    try {
        const user = await MockUser.findById(req.user.userNum);
        
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        res.json({
            success: true,
            data: {
                user: user.toJSON()
            }
        });

    } catch (error) {
        console.error('Get user info error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// POST /api/auth/logout
router.post('/logout', authenticateToken, (req, res) => {
    res.json({
        success: true,
        message: 'Logged out successfully'
    });
});

// POST /api/auth/refresh - Refresh token
router.post('/refresh', authenticateToken, async (req, res) => {
    try {
        const user = await MockUser.findById(req.user.userNum);
        
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Generate new token
        const token = generateToken({
            userID: user.userNum,
            userNum: user.userNum,
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role
        });

        res.json({
            success: true,
            message: 'Token refreshed successfully',
            data: {
                token,
                expiresIn: process.env.JWT_EXPIRE || '24h'
            }
        });

    } catch (error) {
        console.error('Token refresh error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
